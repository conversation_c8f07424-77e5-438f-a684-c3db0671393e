import pandas as pd

ip_mapping_data = pd.read_excel("Node and IP data.xlsx")
input_df = pd.read_excel("RITM Input_enaxarn.xlsx")

def lookup_node(node_names):
    node_list = node_names.split(",")
    node_ips = []
    for node in node_list:
        node_ips.append(ip_mapping_data.loc[ip_mapping_data["Node Name"] == node, "IP"].values[0])
    return "\n".join(node_ips)

input_df["IP_List"] = input_df["Node"].apply(lookup_node)

